"""
数据模型定义
定义I/O点分配系统中的核心数据结构
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple
from enum import Enum


class SignalType(Enum):
    """信号类型枚举"""
    AI = "AI"  # 模拟输入
    AO = "AO"  # 模拟输出
    DI = "DI"  # 数字输入
    DO = "DO"  # 数字输出


@dataclass
class IOPoint:
    """I/O点数据模型"""
    tag: str = ""
    signal_type: SignalType = SignalType.DI
    description: str = ""
    location: str = ""
    cabinet: str = ""
    rack: str = ""
    slot: str = ""
    channel: str = ""
    cable_name: str = ""
    pair_number: int = 1
    is_intrinsic: bool = False
    system: str = ""
    cable_type: str = ""
    wiring_typical: str = ""
    is_spare: bool = False  # 标识是否为spare点

    # 分配结果属性
    allocated_cabinet: Optional[str] = None    # 分配的机柜
    allocated_rail: Optional[str] = None       # 分配的导轨
    allocated_rack: Optional[str] = None       # 分配的机架
    allocated_slot: Optional[str] = None       # 分配的槽位
    allocated_channel: Optional[int] = None    # 分配的通道
    allocated_terminal_block: Optional[str] = None  # 分配的端子排
    allocated_card: Optional[str] = None       # 分配的卡件
    allocated_position: Optional[float] = None # 在导轨上的位置
    allocation_status: str = "未分配"          # 分配状态

    def __post_init__(self):
        """初始化后处理"""
        if isinstance(self.signal_type, str):
            self.signal_type = SignalType(self.signal_type)


@dataclass
class Cable:
    """电缆数据模型"""
    name: str = ""
    pair_size: int = 1
    io_points: List[IOPoint] = field(default_factory=list)
    cable_type: str = ""
    location: str = ""

    def __post_init__(self):
        """初始化后处理"""
        if not isinstance(self.io_points, list):
            self.io_points = []

    def get_used_pairs(self) -> List[int]:
        """获取已使用的线对编号"""
        used_pairs = []
        for io_point in self.io_points:
            if io_point.pair_number > 0:
                used_pairs.append(io_point.pair_number)
        return sorted(list(set(used_pairs)))

    def get_available_pairs(self) -> List[int]:
        """获取可用的线对编号"""
        used_pairs = set(self.get_used_pairs())
        all_pairs = set(range(1, self.pair_size + 1))
        available_pairs = all_pairs - used_pairs
        return sorted(list(available_pairs))


@dataclass
class Rail:
    """导轨数据模型"""
    name: str = ""
    width: float = 500.0  # mm
    height: float = 100.0  # mm
    cabinet: str = ""
    position: int = 0


@dataclass
class Rack:
    """机架数据模型"""
    name: str = ""
    cabinet: str = ""
    rail: str = ""
    max_slots: int = 16
    used_slots: int = 0


@dataclass
class Cabinet:
    """机柜数据模型"""
    name: str = ""
    rails: List[Rail] = field(default_factory=list)
    racks: List[Rack] = field(default_factory=list)
    location: str = ""

    def __post_init__(self):
        """初始化后处理"""
        if not isinstance(self.rails, list):
            self.rails = []
        if not isinstance(self.racks, list):
            self.racks = []


@dataclass
class IOPointAllocation:
    """单个I/O点分配结果"""
    io_point: IOPoint
    allocated_cabinet: str = ""
    allocated_rack: str = ""
    allocated_slot: str = ""
    allocated_channel: str = ""
    status: str = "未分配"
    remarks: str = ""


@dataclass
class AllocationResult:
    """整体分配结果数据模型"""
    success: bool = False
    allocated_points: List[IOPoint] = None
    failed_points: List[IOPoint] = None
    errors: List[str] = None
    warnings: List[str] = None
    summary: Dict[str, Any] = None

    def __post_init__(self):
        if self.allocated_points is None:
            self.allocated_points = []
        if self.failed_points is None:
            self.failed_points = []
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.summary is None:
            self.summary = {}

    def add_error(self, error_message: str):
        """添加错误信息"""
        self.errors.append(error_message)

    def add_warning(self, warning_message: str):
        """添加警告信息"""
        self.warnings.append(warning_message)

    @property
    def allocation_summary(self):
        """allocation_summary属性别名，指向summary"""
        return self.summary

    @allocation_summary.setter
    def allocation_summary(self, value):
        """设置allocation_summary，实际设置summary"""
        self.summary = value
