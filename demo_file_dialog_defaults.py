"""
文件对话框默认路径功能演示
展示IODB和PIDB文件选择如何使用配置中指定的默认文件夹
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_config_paths():
    """演示配置路径功能"""
    print("=" * 60)
    print("文件对话框默认路径功能演示")
    print("=" * 60)
    
    from utils.config_manager_simple import ConfigManager
    
    # 加载配置
    print("1. 加载系统配置...")
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # 显示配置的数据路径
    print("\n2. 配置中的数据路径:")
    data_paths = config.get('data_paths', {})
    
    for key, path in data_paths.items():
        print(f"   {key:20}: {path}")
    
    # 特别展示IODB和PIDB路径
    iodb_path = data_paths.get('iodb', '')
    pidb_path = data_paths.get('pidb', '')
    
    print(f"\n3. 文件选择对话框默认路径:")
    print(f"   IODB文件选择默认路径: {iodb_path}")
    print(f"   PIDB文件选择默认路径: {pidb_path}")
    
    # 检查路径状态
    print(f"\n4. 路径状态检查:")
    
    if os.path.exists(iodb_path):
        print(f"   ✅ IODB路径存在: {iodb_path}")
        files = list(Path(iodb_path).glob("*.xlsx"))
        if files:
            print(f"      发现 {len(files)} 个Excel文件")
            for file in files[:3]:  # 只显示前3个
                print(f"        - {file.name}")
            if len(files) > 3:
                print(f"        ... 还有 {len(files) - 3} 个文件")
        else:
            print(f"      目录为空")
    else:
        print(f"   ⚠️  IODB路径不存在: {iodb_path}")
    
    if os.path.exists(pidb_path):
        print(f"   ✅ PIDB路径存在: {pidb_path}")
        files = list(Path(pidb_path).glob("*.xlsx"))
        if files:
            print(f"      发现 {len(files)} 个Excel文件")
            for file in files[:3]:  # 只显示前3个
                print(f"        - {file.name}")
            if len(files) > 3:
                print(f"        ... 还有 {len(files) - 3} 个文件")
        else:
            print(f"      目录为空")
    else:
        print(f"   ⚠️  PIDB路径不存在: {pidb_path}")
    
    return config


def demo_gui_integration():
    """演示GUI集成"""
    print(f"\n5. GUI组件集成演示:")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # 检查是否已有应用程序实例
        app = QApplication.instance()
        if app is None:
            print("   创建QApplication实例...")
            app = QApplication(sys.argv)
        
        # 演示分配组件的文件对话框
        print("   演示分配组件文件对话框功能...")
        
        from gui.allocation_widget import AllocationWidget
        from utils.config_manager_simple import ConfigManager
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 创建分配组件（不显示）
        widget = AllocationWidget(config)
        
        # 获取默认路径
        data_paths = config.get('data_paths', {})
        iodb_default = data_paths.get('iodb', '')
        pidb_default = data_paths.get('pidb', '')
        
        print(f"   ✅ 分配组件IODB默认路径: {iodb_default}")
        print(f"   ✅ 分配组件PIDB默认路径: {pidb_default}")
        
        # 模拟文件对话框调用
        print("   📁 当用户点击'浏览IODB文件'按钮时:")
        print(f"      文件对话框将打开到: {iodb_default}")
        print("   📁 当用户点击'浏览PIDB文件'按钮时:")
        print(f"      文件对话框将打开到: {pidb_default}")
        
        return True
        
    except ImportError:
        print("   ⚠️  PySide6未安装，跳过GUI演示")
        return False
    except Exception as e:
        print(f"   ❌ GUI演示失败: {e}")
        return False


def demo_config_modification():
    """演示配置修改"""
    print(f"\n6. 配置修改演示:")
    
    from utils.config_manager_simple import ConfigManager
    
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    print("   当前配置:")
    data_paths = config.get('data_paths', {})
    print(f"     IODB: {data_paths.get('iodb', '')}")
    print(f"     PIDB: {data_paths.get('pidb', '')}")
    
    print("\n   如果要修改默认路径，可以:")
    print("   1. 编辑 resources/config.json 文件")
    print("   2. 修改 data_paths 部分:")
    print('      "data_paths": {')
    print('          "iodb": "新的IODB路径",')
    print('          "pidb": "新的PIDB路径"')
    print('      }')
    print("   3. 重启应用程序，新路径将自动生效")
    
    print("\n   路径可以是:")
    print("   • 相对路径（相对于项目根目录）")
    print("   • 绝对路径（完整路径）")
    print("   • 系统会自动将相对路径转换为绝对路径")


def demo_practical_usage():
    """演示实际使用场景"""
    print(f"\n7. 实际使用场景:")
    
    print("   场景1: 项目数据集中管理")
    print("   • 将所有IODB文件放在 04A_IODB 文件夹")
    print("   • 将所有PIDB文件放在 04B_PIDB 文件夹")
    print("   • 文件选择对话框自动打开到对应文件夹")
    print("   • 用户无需每次都导航到正确位置")
    
    print("\n   场景2: 多项目环境")
    print("   • 不同项目可以有不同的配置文件")
    print("   • 每个项目的数据路径可以独立设置")
    print("   • 支持网络共享路径和本地路径")
    
    print("\n   场景3: 团队协作")
    print("   • 团队成员使用统一的文件夹结构")
    print("   • 配置文件可以版本控制")
    print("   • 新成员快速上手，无需手动配置路径")


def main():
    """主演示函数"""
    try:
        # 演示配置路径
        config = demo_config_paths()
        
        # 演示GUI集成
        gui_success = demo_gui_integration()
        
        # 演示配置修改
        demo_config_modification()
        
        # 演示实际使用
        demo_practical_usage()
        
        print("\n" + "=" * 60)
        print("🎉 文件对话框默认路径功能演示完成！")
        print("=" * 60)
        
        print("\n✨ 功能亮点:")
        print("   • 配置驱动的默认路径设置")
        print("   • 自动路径解析和验证")
        print("   • 相对路径自动转换为绝对路径")
        print("   • GUI组件无缝集成")
        print("   • 支持多项目和团队协作")
        
        print("\n🔧 技术特性:")
        print("   • 跨平台路径处理")
        print("   • 配置文件热重载")
        print("   • 路径存在性检查")
        print("   • 目录自动创建")
        print("   • 错误处理和日志记录")
        
        if gui_success:
            print("\n💡 使用提示:")
            print("   • 启动应用程序后，文件选择对话框将自动使用配置的默认路径")
            print("   • 可以通过配置界面或直接编辑config.json来修改路径")
            print("   • 修改后重启应用程序即可生效")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
