"""
基本功能测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from core.report_generator import ReportTemplate, ReportManager, ReportGeneratorFactory
        print("✓ 报表生成器模块导入成功")
    except Exception as e:
        print(f"✗ 报表生成器模块导入失败: {e}")
        return False
    
    try:
        from core.io_report_builder import IOReportBuilder
        print("✓ IO分配表构建器导入成功")
    except Exception as e:
        print(f"✗ IO分配表构建器导入失败: {e}")
        return False
    
    try:
        from core.naming_engine import NamingEngine, NamingContext, NamingElementType
        print("✓ 命名引擎导入成功")
    except Exception as e:
        print(f"✗ 命名引擎导入失败: {e}")
        return False
    
    try:
        from core.pidb_enhanced_reader import PidbEnhancedReader
        print("✓ 增强PIDB读取器导入成功")
    except Exception as e:
        print(f"✗ 增强PIDB读取器导入失败: {e}")
        return False
    
    try:
        from core.template_processor import TemplateProcessor
        print("✓ 模板处理器导入成功")
    except Exception as e:
        print(f"✗ 模板处理器导入失败: {e}")
        return False
    
    return True

def test_naming_engine():
    """测试命名引擎"""
    print("\n测试命名引擎...")
    
    try:
        from core.naming_engine import NamingEngine, NamingContext
        from core.data_models import IOPoint, SignalType
        
        # 创建命名引擎
        naming_engine = NamingEngine()
        print("✓ 命名引擎创建成功")
        
        # 创建测试IO点
        io_point = IOPoint(
            tag="AI_TEST_001",
            signal_type=SignalType.AI,
            description="测试AI点",
            cable_name="CABLE_001",
            pair_number="01",
            is_intrinsic=False,
            system="SYS",
            location="FIELD",
            cable_type="CABLE",
            wiring_typical="TYPICAL_001"
        )
        io_point.allocated_rack = "Rack_1"
        io_point.allocated_slot = 3
        io_point.allocated_channel = 5
        io_point.allocated_cabinet = "1103-SIS-SYS-101"
        
        # 创建命名上下文
        additional_data = {
            'cabinet_numbers': {'1103-SIS-SYS-101': '02'},
            'card_info': {'part_number': '3721'}
        }
        context = NamingContext(io_point, additional_data)
        print("✓ 命名上下文创建成功")
        
        # 测试各种器件命名
        barrier_name = naming_engine.generate_name('barrier', context)
        print(f"✓ 安全栅命名: {barrier_name}")
        
        terminal_name = naming_engine.generate_name('terminal_block', context)
        print(f"✓ 端子排命名: {terminal_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 命名引擎测试失败: {e}")
        return False

def test_report_factory():
    """测试报表工厂"""
    print("\n测试报表工厂...")
    
    try:
        from core.report_generator import ReportGeneratorFactory
        
        # 检查已注册的生成器
        available_types = ReportGeneratorFactory.get_available_types()
        print(f"✓ 可用报表类型: {available_types}")
        
        if 'io_allocation' in available_types:
            print("✓ IO分配表生成器已注册")
            return True
        else:
            print("✗ IO分配表生成器未注册")
            return False
            
    except Exception as e:
        print(f"✗ 报表工厂测试失败: {e}")
        return False

def test_template_creation():
    """测试模板创建"""
    print("\n测试模板创建...")
    
    try:
        from core.report_generator import ReportTemplate
        import tempfile
        import openpyxl
        
        # 创建临时模板文件
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_path = temp_file.name
        
        # 创建简单的Excel文件
        workbook = openpyxl.Workbook()
        workbook.save(temp_path)
        workbook.close()
        
        # 创建报表模板
        template = ReportTemplate(temp_path, "测试模板", "测试描述")
        print("✓ 报表模板创建成功")
        
        # 加载模板
        loaded_workbook = template.load_template()
        print("✓ 模板加载成功")
        
        # 清理
        loaded_workbook.close()
        os.unlink(temp_path)
        
        return True
        
    except Exception as e:
        print(f"✗ 模板创建测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始基本功能测试...\n")
    
    tests = [
        test_imports,
        test_naming_engine,
        test_report_factory,
        test_template_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有基本功能测试通过！")
        return True
    else:
        print("✗ 部分测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
