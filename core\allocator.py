"""
I/O点分配器
负责执行I/O点的智能分配算法
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from core.logger import get_logger
from core.data_models import IOPoint, SignalType
from core.spare_manager import SparePointManager, SpareStrategy


class TerminalBlockStrategy(Enum):
    """端子排分配策略"""
    ETP_FORM = "ETP_FORM"      # ETP形式
    CABLE_FORM = "CABLE_FORM"  # 电缆形式


class CardAllocationStrategy(Enum):
    """卡件分配策略"""
    PRIORITY = "PRIORITY"          # 优先级分配
    LOAD_BALANCE = "LOAD_BALANCE"  # 负载均衡


class RackPrefix(Enum):
    """机架前缀"""
    R = "R"  # R前缀
    C = "C"  # C前缀


@dataclass
class AllocationResult:
    """分配结果"""
    success: bool = False
    allocated_points: List[IOPoint] = None
    failed_points: List[IOPoint] = None
    warnings: List[str] = None
    errors: List[str] = None
    summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.allocated_points is None:
            self.allocated_points = []
        if self.failed_points is None:
            self.failed_points = []
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []
        if self.summary is None:
            self.summary = {}

    def add_error(self, error_message: str):
        """添加错误信息"""
        self.errors.append(error_message)

    def add_warning(self, warning_message: str):
        """添加警告信息"""
        self.warnings.append(warning_message)

    @property
    def allocation_summary(self):
        """allocation_summary属性别名，指向summary"""
        return self.summary

    @allocation_summary.setter
    def allocation_summary(self, value):
        """设置allocation_summary，实际设置summary"""
        self.summary = value


class CardSlotManager:
    """卡件槽位管理器"""
    
    def __init__(self):
        """初始化卡件槽位管理器"""
        self.rack_prefix = RackPrefix.R
        self.etp_upper_suffix = "U"
        self.etp_lower_suffix = "L"
        self.allocation_strategy = CardAllocationStrategy.PRIORITY
    
    def set_rack_prefix(self, prefix: RackPrefix):
        """设置机架前缀"""
        self.rack_prefix = prefix
    
    def set_etp_suffixes(self, upper: str, lower: str):
        """设置ETP后缀"""
        self.etp_upper_suffix = upper
        self.etp_lower_suffix = lower
    
    def set_allocation_strategy(self, strategy: CardAllocationStrategy):
        """设置分配策略"""
        self.allocation_strategy = strategy


class IOAllocator:
    """I/O点分配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化分配器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化子组件
        self.card_slot_manager = CardSlotManager()
        self.spare_manager = SparePointManager(config)

        # 获取分配设置
        self.allocation_settings = config.get('allocation_settings', {})
        self.enable_parttype_matching = self.allocation_settings.get('enable_parttype_matching', True)
        self.enable_detailed_logging = self.allocation_settings.get('enable_detailed_logging', True)
        self.max_allocation_attempts = self.allocation_settings.get('max_allocation_attempts', 1000)

        # 分配策略
        self.terminal_block_strategy = TerminalBlockStrategy.ETP_FORM
    
    def set_terminal_block_strategy(self, strategy: TerminalBlockStrategy):
        """
        设置端子排分配策略
        
        Args:
            strategy: 端子排分配策略
        """
        self.terminal_block_strategy = strategy
        self.logger.info(f"设置端子排分配策略: {strategy.value}")
    
    def allocate_io_points(self, cables: List[Dict[str, Any]], 
                          cabinets: List[Dict[str, Any]], 
                          wiring_typicals: Dict[str, Any]) -> AllocationResult:
        """
        分配I/O点
        
        Args:
            cables: 电缆列表
            cabinets: 机柜列表
            wiring_typicals: 典型回路字典
            
        Returns:
            分配结果
        """
        self.logger.info(f"开始I/O点分配，电缆数: {len(cables)}, 机柜数: {len(cabinets)}")
        
        result = AllocationResult()
        
        try:
            # 初始化分配统计
            total_points = 0
            allocated_count = 0
            failed_count = 0
            
            # 统计总I/O点数
            for cable in cables:
                cable_points = cable.get('io_points', [])
                total_points += len(cable_points)
            
            self.logger.info(f"总计需要分配 {total_points} 个I/O点")
            
            # 执行分配算法
            for cable in cables:
                cable_result = self._allocate_cable(cable, cabinets, wiring_typicals)
                
                # 合并结果
                result.allocated_points.extend(cable_result.allocated_points)
                result.failed_points.extend(cable_result.failed_points)
                result.warnings.extend(cable_result.warnings)
                result.errors.extend(cable_result.errors)
                
                allocated_count += len(cable_result.allocated_points)
                failed_count += len(cable_result.failed_points)
            
            # 计算成功率
            success_rate = (allocated_count / total_points * 100) if total_points > 0 else 0
            
            # 生成分配摘要
            result.summary = {
                'total_points': total_points,
                'allocated_count': allocated_count,
                'failed_count': failed_count,
                'success_rate': success_rate,
                'total_cables': len(cables),
                'strategy': self.terminal_block_strategy.value
            }
            
            # 判断分配是否成功
            result.success = failed_count == 0

            # 如果基本分配成功，生成spare点
            if result.success:
                self.logger.info("开始生成spare点")
                spare_result = self.spare_manager.generate_spare_points(
                    cables=cables,
                    terminal_blocks=[],  # 暂时为空，后续可以从端子排管理器获取
                    wiring_typicals=wiring_typicals,
                    cabinets=cabinets,
                    strategy=SpareStrategy.CABLE_FORM
                )

                if spare_result.success:
                    # 将spare点添加到分配结果中
                    result.allocated_points.extend(spare_result.spare_points)
                    result.summary['spare_points_generated'] = len(spare_result.spare_points)
                    self.logger.info(f"成功生成 {len(spare_result.spare_points)} 个spare点")
                else:
                    result.warnings.extend(spare_result.errors)
                    result.summary['spare_generation_failed'] = True
                    self.logger.warning("Spare点生成失败")

            self.logger.info(f"I/O点分配完成，成功率: {success_rate:.1f}%")
            self.logger.info(f"成功分配: {allocated_count}, 失败: {failed_count}")

            return result
            
        except Exception as e:
            self.logger.error(f"I/O点分配异常: {e}")
            result.errors.append(f"分配过程异常: {e}")
            result.success = False
            return result
    
    def _allocate_cable(self, cable: Dict[str, Any], 
                       cabinets: List[Dict[str, Any]], 
                       wiring_typicals: Dict[str, Any]) -> AllocationResult:
        """
        分配单个电缆的I/O点
        
        Args:
            cable: 电缆数据
            cabinets: 机柜列表
            wiring_typicals: 典型回路字典
            
        Returns:
            分配结果
        """
        cable_name = cable.get('name', 'Unknown')
        io_points = cable.get('io_points', [])
        
        self.logger.debug(f"分配电缆 {cable_name}，包含 {len(io_points)} 个I/O点")
        
        result = AllocationResult()
        
        try:
            # 电缆完整性约束：所有I/O点必须分配到同一机柜
            target_cabinet = self._select_target_cabinet(cable, cabinets)
            
            if not target_cabinet:
                error_msg = f"无法为电缆 {cable_name} 找到合适的机柜"
                result.errors.append(error_msg)
                result.failed_points.extend(io_points)
                return result
            
            # 为每个I/O点分配具体位置
            for io_point in io_points:
                allocation_success = self._allocate_single_point(
                    io_point, target_cabinet, wiring_typicals
                )
                
                if allocation_success:
                    result.allocated_points.append(io_point)
                    self.logger.debug(f"成功分配I/O点: {io_point.tag}")
                else:
                    result.failed_points.append(io_point)
                    warning_msg = f"I/O点 {io_point.tag} 分配失败"
                    result.warnings.append(warning_msg)
                    self.logger.warning(warning_msg)
            
            return result
            
        except Exception as e:
            self.logger.error(f"分配电缆 {cable_name} 异常: {e}")
            result.errors.append(f"电缆分配异常: {e}")
            result.failed_points.extend(io_points)
            return result
    
    def _select_target_cabinet(self, cable: Dict[str, Any], 
                              cabinets: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        选择目标机柜
        
        Args:
            cable: 电缆数据
            cabinets: 机柜列表
            
        Returns:
            选中的机柜，如果没有合适的返回None
        """
        cable_name = cable.get('name', 'Unknown')
        io_points = cable.get('io_points', [])
        
        if not io_points:
            return None
        
        # 简化选择逻辑：选择第一个可用的机柜
        for cabinet in cabinets:
            cabinet_name = cabinet.get('name', '')
            rails = cabinet.get('rails', [])
            
            # 检查机柜是否有足够的空间
            if len(rails) > 0:
                self.logger.debug(f"选择机柜 {cabinet_name} 用于电缆 {cable_name}")
                return cabinet
        
        return None
    
    def _allocate_single_point(self, io_point: IOPoint, 
                              cabinet: Dict[str, Any], 
                              wiring_typicals: Dict[str, Any]) -> bool:
        """
        分配单个I/O点
        
        Args:
            io_point: I/O点对象
            cabinet: 目标机柜
            wiring_typicals: 典型回路字典
            
        Returns:
            分配是否成功
        """
        try:
            # 简化分配逻辑
            cabinet_name = cabinet.get('name', '')
            rails = cabinet.get('rails', [])
            
            if not rails:
                return False
            
            # 选择第一个可用的导轨
            target_rail = rails[0]
            rail_name = target_rail.get('name', 'Rail1')
            
            # 更新I/O点的分配信息
            io_point.cabinet = cabinet_name
            io_point.rack = f"{self.card_slot_manager.rack_prefix.value}01"
            io_point.slot = "01"
            io_point.channel = "01"
            
            return True
            
        except Exception as e:
            self.logger.error(f"分配I/O点 {io_point.tag} 异常: {e}")
            return False
