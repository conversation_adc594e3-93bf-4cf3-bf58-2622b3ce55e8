# EWReborn - 工业自动化 I/O 点分配系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PySide6](https://img.shields.io/badge/PySide6-6.0+-green.svg)](https://pypi.org/project/PySide6/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个专业的工业自动化 I/O 点分配系统，提供现代化的图形界面和完整的项目管理功能。

## ✨ 主要特性

### 🎯 核心功能
- **智能 I/O 点分配** - 自动化的输入输出点分配和管理
- **电缆管理系统** - 完整的电缆配置和验证功能
- **项目生命周期管理** - 从创建到交付的完整项目管理
- **报表生成引擎** - 自动生成专业的工程报表

### 🎨 用户界面
- **现代化 GUI** - 基于 PySide6 的响应式界面
- **Material Design 主题** - 支持多种主题切换
- **XML 编辑器** - 内置的配置文件编辑器
- **实时进度监控** - 可视化的任务进度跟踪

### 🔧 技术特性
- **模块化架构** - 清晰的代码组织和可维护性
- **完善的测试套件** - 29 个测试脚本覆盖各个功能模块
- **详细的文档** - 包含需求文档、使用说明和 API 文档
- **跨平台支持** - 支持 Windows、Linux 和 macOS

## 🚀 快速开始

### 环境要求
- Python 3.8 或更高版本
- Windows 10/11 (推荐) 或 Linux/macOS

### 安装步骤

1. **克隆仓库**
   ```bash
   git clone https://github.com/ATLASste/EWReborn.git
   cd EWReborn
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行应用程序**
   ```bash
   python main.py
   ```

## 📁 项目结构

```
EWReborn/
├── core/                    # 核心业务逻辑
│   ├── allocator.py        # I/O点分配器
│   ├── data_models.py      # 数据模型
│   ├── project_manager.py  # 项目管理器
│   └── ...
├── gui/                     # 用户界面组件
│   ├── main_window.py      # 主窗口
│   ├── allocation_widget.py # 分配界面
│   └── ...
├── tests/                   # 测试套件
│   ├── test_modules.py     # 模块测试
│   ├── test_gui.py         # GUI测试
│   └── ...
├── docs/                    # 项目文档
├── data/                    # 数据文件
├── resources/               # 资源文件
└── main.py                  # 主程序入口
```

## 🧪 运行测试

### 快速测试
```bash
# 基本功能测试
python tests/test_simple.py

# 完整模块测试
python tests/test_modules.py

# GUI 组件测试
python tests/test_gui.py
```

### 功能演示
```bash
# 文件对话框功能演示
python tests/demo_file_dialog_defaults.py

# 命名配置演示
python tests/demo_naming_config.py
```

## 📚 文档

- [项目需求文档](docs/项目需求文档_EWReborn_I_O点分配系统.md)
- [测试套件说明](tests/README.md)
- [Material 主题指南](MATERIAL_THEME_GUIDE.md)
- [项目整理总结](PROJECT_ORGANIZATION_SUMMARY.md)

## 🛠️ 开发

### 开发环境设置
1. 安装开发依赖
2. 配置 IDE (推荐 VS Code)
3. 运行测试确保环境正常

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 作者

- **ATLASste** - *项目创建者* - [GitHub](https://github.com/ATLASste)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**注意**: 这是一个专业的工业自动化软件，请在生产环境中谨慎使用。
